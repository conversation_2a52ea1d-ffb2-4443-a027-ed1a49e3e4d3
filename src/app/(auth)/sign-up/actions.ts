"use server";

import { redirect } from "next/navigation";

import {
  createServerActionSupabaseClient,
  EMAIL_VERIFICATION_REDIRECT_URL,
} from "@/app/dependencies";
import SupabaseAuthService from "@/supabase/SupabaseAuthService";

import { signUpSchema } from "./schema";

export type SignUpFormState = {
  error?: string;
  fieldErrors?: Record<string, string[]>;
};

export async function signUp(
  prevState: SignUpFormState,
  formData: FormData
): Promise<SignUpFormState> {
  const parsed = signUpSchema.safeParse({
    email: formData.get("email"),
    password: formData.get("password"),
    acceptedTerms: formData.get("acceptedTerms") === "on",
  });

  if (!parsed.success) {
    return { fieldErrors: parsed.error.flatten().fieldErrors };
  }

  const { email, password } = parsed.data;

  try {
    const supabase = await createServerActionSupabaseClient();
    const authService = new SupabaseAuthService(supabase.auth);

    const { data: authUser, error } = await authService.signUp({
      email,
      password,
      redirectTo: EMAIL_VERIFICATION_REDIRECT_URL,
    });

    if (error) {
      return { error: error.message };
    }

    if (!authUser) {
      return { error: "Failed to create user" };
    }

    // let user = await userRepository.findById(authUser.id);

    // if (user) {
    //   return { error: "User already exists" };
    // }

    // user = createUser({
    //   id: authUser.id,
    //   email: authUser.email,
    //   acceptedTermsAndConditionsAt: new Date(),
    //   status: UserStatus.WAITING_ONBOARDING,
    // });

    // await userRepository.add(user);
  } catch (error) {
    return { error: "An unexpected error occurred" };
  }

  redirect(`/verify-email?email=${encodeURIComponent(email)}`);
}
