import assert from "node:assert";

import type { UserId } from "./user";

export enum VerificationStatus {
  PendingOnboarding = "pending_onboarding",
  PendingVerification = "pending_verification",
  Verified = "verified",
  Rejected = "rejected",
}

export enum Specialty {
  Hair = "hair",
  Nails = "nails",
  Braids = "braids",
  Barbering = "barbering",
  Skin = "skin",
  Brows = "brows",
  Lashes = "lashes",
  Makeup = "makeup",
}

export type Address = {
  street: string;
  city: string;
  state: string;
  zipCode: string;
};

export type IBP = {
  userId: UserId;
  firstName: string;
  lastName: string;
  businessName: string | null;
  address: Address;
  phoneNumber: string;
  specialties: [Specialty, ...Specialty[]];
  licenseNumber: string;
  verificationStatus: VerificationStatus;
  onboarding: OnboardingState;
  cancellationPolicy: CancellationPolicy;
  services: Service[];
  availability: WeeklyAvailability;
  timeOffs: TimeOff[];
  appointments: Appointment[];
  createdAt: Date;
  updatedAt: Date;
};

export enum OnboardingStep {
  Introduction = "introduction",
  PersonalInfo = "personal_info",
  Specialties = "specialties",
  License = "license",
  Review = "review",
}

export type OnboardingState = {
  completedSteps: OnboardingStep[];
  isComplete: boolean;
};

export type ServiceId = string;
export type AddOnId = string;

export type DurationMinutes = number;
export type PriceCents = number;

export type ServiceCategory = Specialty;

export type AddOn = {
  id: AddOnId;
  name: string;
  description: string | null;
  durationMinutes: DurationMinutes;
  priceCents: PriceCents;
  isRequired: boolean;
};

export type Service = {
  id: ServiceId;
  name: string;
  description: string | null;
  category: ServiceCategory;
  durationMinutes: DurationMinutes;
  priceCents: PriceCents;
  addOns: AddOn[];
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
};

export type HoursBeforeAppointment = number;

export enum FeeType {
  Flat = "flat",
  Percentage = "percentage",
}

export type CancellationFee =
  | { type: FeeType.Flat; amountCents: PriceCents }
  | { type: FeeType.Percentage; percentage: number };

export type CancellationPolicy = {
  freeCancellationWindowHours: HoursBeforeAppointment;
  lateCancellationFee: CancellationFee;
  noShowFee: CancellationFee;
};

export type CancellationOutcome =
  | { type: CancellationType.Free; refundAmount: PriceCents }
  | { type: CancellationType.Late; feeCents: PriceCents }
  | { type: CancellationType.NoShow; feeCents: PriceCents };

export type TimeOffId = string;

export enum DayOfWeek {
  Monday = "monday",
  Tuesday = "tuesday",
  Wednesday = "wednesday",
  Thursday = "thursday",
  Friday = "friday",
  Saturday = "saturday",
  Sunday = "sunday",
}

export type TimeSlot = {
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
};

export type DayAvailability = {
  dayOfWeek: DayOfWeek;
  isAvailable: boolean;
  timeSlots: TimeSlot[];
};

export type WeeklyAvailability = {
  schedule: [DayAvailability, ...DayAvailability[]]; // At least one day required
};

export type TimeOff = {
  id: TimeOffId;
  startDate: Date;
  endDate: Date;
  reason: string | null;
  createdAt: Date;
};

export type AppointmentId = string;

export enum AppointmentStatus {
  Pending = "pending",
  Scheduled = "scheduled",
  Completed = "completed",
  Cancelled = "cancelled",
  NoShow = "no_show",
}

export enum CancellationType {
  Free = "free",
  Late = "late",
  NoShow = "no_show",
}

export type SelectedAddOn = {
  addOnId: AddOnId;
  name: string;
  priceCents: PriceCents;
  durationMinutes: DurationMinutes;
};

export type AppointmentReschedule = {
  previousDate: Date;
  previousStartTime: string;
  rescheduledDate: Date;
  rescheduledStartTime: string;
};

export type Appointment = {
  id: AppointmentId;
  clientId: UserId;
  serviceId: ServiceId;
  serviceName: string;
  selectedAddOns: SelectedAddOn[];
  scheduledDate: Date;
  startTime: string; // HH:mm format
  endTime: string; // HH:mm format
  totalDurationMinutes: DurationMinutes;
  totalPriceCents: PriceCents;
  status: AppointmentStatus;
  cancelledAt: Date | null;
  cancellationType: CancellationType | null;
  reschedules: AppointmentReschedule[];
  createdAt: Date;
  updatedAt: Date;
};

type CreateIBPParams = {
  userId: UserId;
};

export function createIBP(params: CreateIBPParams): IBP {
  const now = new Date();
  return {
    userId: params.userId,
    firstName: "",
    lastName: "",
    businessName: null,
    address: { street: "", city: "", state: "", zipCode: "" },
    phoneNumber: "",
    specialties: [Specialty.Hair], // Placeholder, will be set during onboarding
    licenseNumber: "",
    verificationStatus: VerificationStatus.PendingOnboarding,
    onboarding: {
      completedSteps: [],
      isComplete: false,
    },
    cancellationPolicy: {
      freeCancellationWindowHours: 24,
      lateCancellationFee: { type: FeeType.Flat, amountCents: 0 },
      noShowFee: { type: FeeType.Flat, amountCents: 0 },
    },
    services: [],
    availability: {
      schedule: [
        { dayOfWeek: DayOfWeek.Monday, isAvailable: false, timeSlots: [] },
      ],
    },
    timeOffs: [],
    appointments: [],
    createdAt: now,
    updatedAt: now,
  };
}

export function startOnboarding(ibp: IBP): IBP {
  return {
    ...ibp,
    onboarding: {
      ...ibp.onboarding,
      completedSteps: addCompletedStep(
        ibp.onboarding.completedSteps,
        OnboardingStep.Introduction
      ),
    },
    updatedAt: new Date(),
  };
}

type CompletePersonalInfoParams = {
  firstName: string;
  lastName: string;
  businessName: string | null;
  phoneNumber: string;
  address: {
    street: string;
    city: string;
    state: string;
    zipCode: string;
  };
};

export function completePersonalInfo(
  ibp: IBP,
  params: CompletePersonalInfoParams
): IBP {
  return {
    ...ibp,
    firstName: params.firstName,
    lastName: params.lastName,
    businessName: params.businessName,
    phoneNumber: params.phoneNumber,
    address: params.address,
    onboarding: {
      ...ibp.onboarding,
      completedSteps: addCompletedStep(
        ibp.onboarding.completedSteps,
        OnboardingStep.PersonalInfo
      ),
    },
    updatedAt: new Date(),
  };
}

type SetSpecialtiesParams = {
  specialties: [Specialty, ...Specialty[]];
};

export function setSpecialties(ibp: IBP, params: SetSpecialtiesParams): IBP {
  assert(params.specialties.length > 0, "At least one specialty is required");

  return {
    ...ibp,
    specialties: params.specialties,
    onboarding: {
      ...ibp.onboarding,
      completedSteps: addCompletedStep(
        ibp.onboarding.completedSteps,
        OnboardingStep.Specialties
      ),
    },
    updatedAt: new Date(),
  };
}

type SubmitLicenseParams = {
  licenseNumber: string;
};

export function submitLicense(ibp: IBP, params: SubmitLicenseParams): IBP {
  assert(params.licenseNumber.length > 0, "License number is required");

  return {
    ...ibp,
    licenseNumber: params.licenseNumber,
    verificationStatus: VerificationStatus.PendingVerification,
    onboarding: {
      ...ibp.onboarding,
      completedSteps: addCompletedStep(
        ibp.onboarding.completedSteps,
        OnboardingStep.License
      ),
    },
    updatedAt: new Date(),
  };
}

export function completeOnboarding(ibp: IBP): IBP {
  return {
    ...ibp,
    onboarding: {
      ...ibp.onboarding,
      completedSteps: addCompletedStep(
        ibp.onboarding.completedSteps,
        OnboardingStep.Review
      ),
      isComplete: true,
    },
    updatedAt: new Date(),
  };
}

export function verifyIBP(ibp: IBP): IBP {
  assert(
    ibp.verificationStatus === VerificationStatus.PendingVerification,
    "IBP must be pending verification"
  );

  return {
    ...ibp,
    verificationStatus: VerificationStatus.Verified,
    updatedAt: new Date(),
  };
}

export function rejectIBP(ibp: IBP): IBP {
  assert(
    ibp.verificationStatus === VerificationStatus.PendingVerification,
    "IBP must be pending verification"
  );

  return {
    ...ibp,
    verificationStatus: VerificationStatus.Rejected,
    updatedAt: new Date(),
  };
}

type AddServiceParams = {
  id: ServiceId;
  name: string;
  description: string | null;
  category: ServiceCategory;
  durationMinutes: number;
  priceCents: number;
};

export function addService(ibp: IBP, params: AddServiceParams): IBP {
  assert(
    ibp.verificationStatus === VerificationStatus.Verified,
    "IBP must be verified to add services"
  );
  assert(params.durationMinutes > 0, "Duration must be positive");
  assert(params.priceCents >= 0, "Price must be non-negative");

  const now = new Date();
  const newService: Service = {
    id: params.id,
    name: params.name,
    description: params.description,
    category: params.category,
    durationMinutes: params.durationMinutes,
    priceCents: params.priceCents,
    addOns: [],
    isActive: true,
    createdAt: now,
    updatedAt: now,
  };

  return {
    ...ibp,
    services: [...ibp.services, newService],
    updatedAt: now,
  };
}

type UpdateServiceParams = {
  serviceId: ServiceId;
  name: string;
  description: string | null;
  category: ServiceCategory;
  durationMinutes: DurationMinutes;
  priceCents: PriceCents;
};

export function updateService(ibp: IBP, params: UpdateServiceParams): IBP {
  const serviceIndex = ibp.services.findIndex((s) => s.id === params.serviceId);
  // eslint-disable-next-line sonarjs/no-duplicate-string
  assert(serviceIndex !== -1, "Service not found");

  const service = ibp.services[serviceIndex];
  const now = new Date();

  const updatedService: Service = {
    ...service,
    name: params.name ?? service.name,
    description: params.description,
    category: params.category,
    durationMinutes: params.durationMinutes,
    priceCents: params.priceCents,
    updatedAt: now,
  };

  const updatedServices = [...ibp.services];
  updatedServices[serviceIndex] = updatedService;

  return {
    ...ibp,
    services: updatedServices,
    updatedAt: now,
  };
}

type DeactivateServiceParams = {
  serviceId: ServiceId;
};

export function deactivateService(
  ibp: IBP,
  params: DeactivateServiceParams
): IBP {
  const serviceIndex = ibp.services.findIndex((s) => s.id === params.serviceId);
  assert(serviceIndex !== -1, "Service not found");
  assert(ibp.services[serviceIndex].isActive, "Service is already inactive");

  const now = new Date();
  const updatedServices = [...ibp.services];
  updatedServices[serviceIndex] = {
    ...updatedServices[serviceIndex],
    isActive: false,
    updatedAt: now,
  };

  return {
    ...ibp,
    services: updatedServices,
    updatedAt: now,
  };
}

type ActivateServiceParams = {
  serviceId: ServiceId;
};

export function activateService(ibp: IBP, params: ActivateServiceParams): IBP {
  const serviceIndex = ibp.services.findIndex((s) => s.id === params.serviceId);
  assert(serviceIndex !== -1, "Service not found");
  assert(!ibp.services[serviceIndex].isActive, "Service is already active");

  const now = new Date();
  const updatedServices = [...ibp.services];
  updatedServices[serviceIndex] = {
    ...updatedServices[serviceIndex],
    isActive: true,
    updatedAt: now,
  };

  return {
    ...ibp,
    services: updatedServices,
    updatedAt: now,
  };
}

type AddServiceAddOnParams = {
  serviceId: ServiceId;
  addOn: {
    id: AddOnId;
    name: string;
    description?: string | null;
    durationMinutes: number;
    priceCents: number;
    isRequired: boolean;
  };
};

export function addServiceAddOn(ibp: IBP, params: AddServiceAddOnParams): IBP {
  const serviceIndex = ibp.services.findIndex((s) => s.id === params.serviceId);
  assert(serviceIndex !== -1, "Service not found");

  const service = ibp.services[serviceIndex];
  const now = new Date();

  const newAddOn: AddOn = {
    id: params.addOn.id,
    name: params.addOn.name,
    description: params.addOn.description ?? null,
    durationMinutes: params.addOn.durationMinutes,
    priceCents: params.addOn.priceCents,
    isRequired: params.addOn.isRequired,
  };

  const updatedService: Service = {
    ...service,
    addOns: [...service.addOns, newAddOn],
    updatedAt: now,
  };

  const updatedServices = [...ibp.services];
  updatedServices[serviceIndex] = updatedService;

  return {
    ...ibp,
    services: updatedServices,
    updatedAt: now,
  };
}

type UpdateAddOnParams = {
  serviceId: ServiceId;
  addOnId: AddOnId;
  name: string;
  description: string | null;
  durationMinutes: DurationMinutes;
  priceCents: PriceCents;
  isRequired: boolean;
};

export function updateAddOn(ibp: IBP, params: UpdateAddOnParams): IBP {
  const serviceIndex = ibp.services.findIndex((s) => s.id === params.serviceId);
  assert(serviceIndex !== -1, "Service not found");

  const service = ibp.services[serviceIndex];
  const addOnIndex = service.addOns.findIndex((a) => a.id === params.addOnId);
  assert(addOnIndex !== -1, "Add-on not found");

  const addOn = service.addOns[addOnIndex];
  const now = new Date();

  const updatedAddOn: AddOn = {
    ...addOn,
    name: params.name,
    description: params.description,
    durationMinutes: params.durationMinutes,
    priceCents: params.priceCents,
    isRequired: params.isRequired,
  };

  const updatedAddOns = [...service.addOns];
  updatedAddOns[addOnIndex] = updatedAddOn;

  const updatedService: Service = {
    ...service,
    addOns: updatedAddOns,
    updatedAt: now,
  };

  const updatedServices = [...ibp.services];
  updatedServices[serviceIndex] = updatedService;

  return {
    ...ibp,
    services: updatedServices,
    updatedAt: now,
  };
}

type RemoveAddOnParams = {
  serviceId: ServiceId;
  addOnId: AddOnId;
};

export function removeAddOn(ibp: IBP, params: RemoveAddOnParams): IBP {
  const serviceIndex = ibp.services.findIndex((s) => s.id === params.serviceId);
  assert(serviceIndex !== -1, "Service not found");

  const service = ibp.services[serviceIndex];
  const addOnIndex = service.addOns.findIndex((a) => a.id === params.addOnId);
  assert(addOnIndex !== -1, "Add-on not found");

  const now = new Date();
  const updatedAddOns = service.addOns.filter((a) => a.id !== params.addOnId);

  const updatedService: Service = {
    ...service,
    addOns: updatedAddOns,
    updatedAt: now,
  };

  const updatedServices = [...ibp.services];
  updatedServices[serviceIndex] = updatedService;

  return {
    ...ibp,
    services: updatedServices,
    updatedAt: now,
  };
}

type SetWeeklyAvailabilityParams = {
  schedule: [DayAvailability, ...DayAvailability[]];
};

export function setWeeklyAvailability(
  ibp: IBP,
  params: SetWeeklyAvailabilityParams
): IBP {
  assert(
    params.schedule.length > 0,
    "At least one day of availability is required"
  );

  return {
    ...ibp,
    availability: { schedule: params.schedule },
    updatedAt: new Date(),
  };
}

type AddTimeOffParams = {
  id: TimeOffId;
  startDate: Date;
  endDate: Date;
  reason?: string | null;
};

export function addTimeOff(ibp: IBP, params: AddTimeOffParams): IBP {
  assert(
    params.endDate >= params.startDate,
    "End date must be after start date"
  );

  const newTimeOff: TimeOff = {
    id: params.id,
    startDate: params.startDate,
    endDate: params.endDate,
    reason: params.reason ?? null,
    createdAt: new Date(),
  };

  return {
    ...ibp,
    timeOffs: [...ibp.timeOffs, newTimeOff],
    updatedAt: new Date(),
  };
}

type RemoveTimeOffParams = {
  timeOffId: TimeOffId;
};

export function removeTimeOff(ibp: IBP, params: RemoveTimeOffParams): IBP {
  const timeOffIndex = ibp.timeOffs.findIndex((t) => t.id === params.timeOffId);
  assert(timeOffIndex !== -1, "Time off not found");

  return {
    ...ibp,
    timeOffs: ibp.timeOffs.filter((t) => t.id !== params.timeOffId),
    updatedAt: new Date(),
  };
}

type SetCancellationPolicyParams = {
  freeCancellationWindowHours: HoursBeforeAppointment;
  lateCancellationFee: CancellationFee;
  noShowFee: CancellationFee;
};

export function setCancellationPolicy(
  ibp: IBP,
  params: SetCancellationPolicyParams
): IBP {
  assert(
    params.freeCancellationWindowHours >= 0,
    "Free cancellation window must be non-negative"
  );

  return {
    ...ibp,
    cancellationPolicy: {
      freeCancellationWindowHours: params.freeCancellationWindowHours,
      lateCancellationFee: params.lateCancellationFee,
      noShowFee: params.noShowFee,
    },
    updatedAt: new Date(),
  };
}

type CreateAppointmentParams = {
  id: AppointmentId;
  clientId: UserId;
  serviceId: ServiceId;
  selectedAddOnIds: AddOnId[];
  scheduledDate: Date;
  startTime: string;
};

export function createAppointment(
  ibp: IBP,
  params: CreateAppointmentParams
): IBP {
  const service = ibp.services.find((s) => s.id === params.serviceId);
  assert(service !== undefined, "Service not found");
  assert(service.isActive, "Service is not active");

  const requiredAddOns = service.addOns.filter((a) => a.isRequired);
  const optionalSelectedAddOns = service.addOns.filter(
    (a) => !a.isRequired && params.selectedAddOnIds.includes(a.id)
  );
  const allSelectedAddOns = [...requiredAddOns, ...optionalSelectedAddOns];

  const totalDuration =
    service.durationMinutes +
    allSelectedAddOns.reduce((sum, a) => sum + a.durationMinutes, 0);
  const totalPrice =
    service.priceCents +
    allSelectedAddOns.reduce((sum, a) => sum + a.priceCents, 0);

  const endTime = calculateEndTime(params.startTime, totalDuration);

  const now = new Date();
  const newAppointment: Appointment = {
    id: params.id,
    clientId: params.clientId,
    serviceId: params.serviceId,
    serviceName: service.name,
    selectedAddOns: allSelectedAddOns.map((a) => ({
      addOnId: a.id,
      name: a.name,
      priceCents: a.priceCents,
      durationMinutes: a.durationMinutes,
    })),
    scheduledDate: params.scheduledDate,
    startTime: params.startTime,
    endTime,
    totalDurationMinutes: totalDuration,
    totalPriceCents: totalPrice,
    status: AppointmentStatus.Pending,
    cancelledAt: null,
    cancellationType: null,
    reschedules: [],
    createdAt: now,
    updatedAt: now,
  };

  return {
    ...ibp,
    appointments: [...ibp.appointments, newAppointment],
    updatedAt: now,
  };
}

type ConfirmAppointmentParams = {
  appointmentId: AppointmentId;
};

export function confirmAppointment(
  ibp: IBP,
  params: ConfirmAppointmentParams
): IBP {
  const appointmentIndex = ibp.appointments.findIndex(
    (a) => a.id === params.appointmentId
  );
  assert(appointmentIndex !== -1, "Appointment not found");
  assert(
    ibp.appointments[appointmentIndex].status === AppointmentStatus.Pending,
    "Appointment must be pending"
  );

  const now = new Date();
  const updatedAppointments = [...ibp.appointments];
  updatedAppointments[appointmentIndex] = {
    ...updatedAppointments[appointmentIndex],
    status: AppointmentStatus.Scheduled,
    updatedAt: now,
  };

  return {
    ...ibp,
    appointments: updatedAppointments,
    updatedAt: now,
  };
}

type CancelAppointmentParams = {
  appointmentId: AppointmentId;
};

export function cancelAppointment(
  ibp: IBP,
  params: CancelAppointmentParams
): IBP {
  const appointmentIndex = ibp.appointments.findIndex(
    (a) => a.id === params.appointmentId
  );
  assert(appointmentIndex !== -1, "Appointment not found");

  const appointment = ibp.appointments[appointmentIndex];
  assert(
    appointment.status === AppointmentStatus.Pending ||
      appointment.status === AppointmentStatus.Scheduled,
    "Appointment cannot be cancelled"
  );

  const now = new Date();

  const cancellationType = calculateCancellationType(
    appointment,
    ibp.cancellationPolicy,
    now
  );

  const updatedAppointments = [...ibp.appointments];
  updatedAppointments[appointmentIndex] = {
    ...appointment,
    status: AppointmentStatus.Cancelled,
    cancelledAt: now,
    cancellationType,
    updatedAt: now,
  };

  return {
    ...ibp,
    appointments: updatedAppointments,
    updatedAt: now,
  };
}

type MarkNoShowParams = {
  appointmentId: AppointmentId;
};

export function markNoShow(ibp: IBP, params: MarkNoShowParams): IBP {
  const appointmentIndex = ibp.appointments.findIndex(
    (a) => a.id === params.appointmentId
  );
  assert(appointmentIndex !== -1, "Appointment not found");

  const appointment = ibp.appointments[appointmentIndex];
  assert(
    appointment.status === AppointmentStatus.Scheduled,
    "Only scheduled appointments can be marked as no-show"
  );

  const now = new Date();
  const updatedAppointments = [...ibp.appointments];
  updatedAppointments[appointmentIndex] = {
    ...appointment,
    status: AppointmentStatus.NoShow,
    cancellationType: CancellationType.NoShow,
    updatedAt: now,
  };

  return {
    ...ibp,
    appointments: updatedAppointments,
    updatedAt: now,
  };
}

type RescheduleAppointmentParams = {
  appointmentId: AppointmentId;
  newDate: Date;
  newStartTime: string;
};

export function rescheduleAppointment(
  ibp: IBP,
  params: RescheduleAppointmentParams
): IBP {
  const appointmentIndex = ibp.appointments.findIndex(
    (a) => a.id === params.appointmentId
  );
  assert(appointmentIndex !== -1, "Appointment not found");

  const appointment = ibp.appointments[appointmentIndex];
  assert(
    appointment.status === AppointmentStatus.Pending ||
      appointment.status === AppointmentStatus.Scheduled,
    "Appointment cannot be rescheduled"
  );

  const newEndTime = calculateEndTime(
    params.newStartTime,
    appointment.totalDurationMinutes
  );

  const rescheduleRecord: AppointmentReschedule = {
    previousDate: appointment.scheduledDate,
    previousStartTime: appointment.startTime,
    rescheduledDate: params.newDate,
    rescheduledStartTime: params.newStartTime,
  };

  const now = new Date();
  const updatedAppointments = [...ibp.appointments];
  updatedAppointments[appointmentIndex] = {
    ...appointment,
    scheduledDate: params.newDate,
    startTime: params.newStartTime,
    endTime: newEndTime,
    reschedules: [...appointment.reschedules, rescheduleRecord],
    updatedAt: now,
  };

  return {
    ...ibp,
    appointments: updatedAppointments,
    updatedAt: now,
  };
}

type CompleteAppointmentParams = {
  appointmentId: AppointmentId;
};

export function completeAppointment(
  ibp: IBP,
  params: CompleteAppointmentParams
): IBP {
  const appointmentIndex = ibp.appointments.findIndex(
    (a) => a.id === params.appointmentId
  );
  assert(appointmentIndex !== -1, "Appointment not found");

  const appointment = ibp.appointments[appointmentIndex];
  assert(
    appointment.status === AppointmentStatus.Scheduled,
    "Only scheduled appointments can be completed"
  );

  const now = new Date();
  const updatedAppointments = [...ibp.appointments];
  updatedAppointments[appointmentIndex] = {
    ...appointment,
    status: AppointmentStatus.Completed,
    updatedAt: now,
  };

  return {
    ...ibp,
    appointments: updatedAppointments,
    updatedAt: now,
  };
}

function addCompletedStep(
  completedSteps: OnboardingStep[],
  step: OnboardingStep
): OnboardingStep[] {
  if (completedSteps.includes(step)) {
    return completedSteps;
  }
  return [...completedSteps, step];
}

function calculateEndTime(startTime: string, durationMinutes: number): string {
  const [hours, minutes] = startTime.split(":").map(Number);
  const totalMinutes = hours * 60 + minutes + durationMinutes;
  const endHours = Math.floor(totalMinutes / 60) % 24;
  const endMinutes = totalMinutes % 60;
  return `${endHours.toString().padStart(2, "0")}:${endMinutes.toString().padStart(2, "0")}`;
}

function calculateCancellationType(
  appointment: Appointment,
  cancellationPolicy: CancellationPolicy,
  cancelledAt: Date
): CancellationType.Free | CancellationType.Late {
  const appointmentDateTime = new Date(appointment.scheduledDate);
  const [hours, minutes] = appointment.startTime.split(":").map(Number);
  appointmentDateTime.setHours(hours, minutes, 0, 0);
  const timeDifferenceMs =
    appointmentDateTime.getTime() - cancelledAt.getTime();
  const hoursUntilAppointment = timeDifferenceMs / (1000 * 60 * 60);

  if (hoursUntilAppointment >= cancellationPolicy.freeCancellationWindowHours) {
    return CancellationType.Free;
  }

  return CancellationType.Late;
}
