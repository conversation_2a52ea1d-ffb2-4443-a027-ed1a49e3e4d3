export enum UserRole {
  IBP = "ibp",
  Client = "client",
}

export type UserId = string;

export type User = {
  id: UserId;
  email: string;
  role: UserRole;
  createdAt: Date;
};

type CreateUserParams = {
  id: UserId;
  email: string;
  role: UserRole;
};

export function createUser(params: CreateUserParams): User {
  return {
    id: params.id,
    email: params.email,
    role: params.role,
    createdAt: new Date(),
  };
}
