import type { UserId } from "./user";

export type Client = {
  userId: UserId;
  firstName: string;
  lastName: string;
  phoneNumber: string | null;
  createdAt: Date;
  updatedAt: Date;
};

type CreateClientParams = {
  userId: UserId;
  firstName: string;
  lastName: string;
  phoneNumber: string | null;
};

export function createClient(params: CreateClientParams): Client {
  const now = new Date();
  return {
    userId: params.userId,
    firstName: params.firstName,
    lastName: params.lastName,
    phoneNumber: params.phoneNumber,
    createdAt: now,
    updatedAt: now,
  };
}

type UpdateClientProfileParams = {
  firstName?: string;
  lastName?: string;
  phoneNumber?: string | null;
};

export function updateClientProfile(
  client: Client,
  params: UpdateClientProfileParams
): Client {
  return {
    ...client,
    firstName: params.firstName ?? client.firstName,
    lastName: params.lastName ?? client.lastName,
    phoneNumber:
      params.phoneNumber !== undefined
        ? params.phoneNumber
        : client.phoneNumber,
    updatedAt: new Date(),
  };
}
